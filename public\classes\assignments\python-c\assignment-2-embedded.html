<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài Tập 2: Python và Cài Đặt Môi Trường - Python C (Embedded)</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .assignment-container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #D35400, #E67E22);
            color: white;
            border-radius: 15px;
        }
        
        .assignment-header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .timer-info {
            background: white;
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .timer {
            font-size: 1.5rem;
            font-weight: bold;
            color: #D35400;
        }
        
        .quiz-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .question {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            display: none;
        }
        
        .question.active {
            display: block;
        }
        
        .question-number {
            color: #D35400;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .question-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .options {
            list-style: none;
            padding: 0;
        }
        
        .option {
            margin-bottom: 10px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            border-color: #D35400;
            background-color: #f8f9fa;
        }
        
        .option.selected {
            border-color: #D35400;
            background-color: #e3f2fd;
        }
        
        .option input[type="radio"] {
            margin-right: 10px;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
        }
        
        .nav-btn {
            background: #D35400;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }
        
        .nav-btn:hover {
            background: #3367D6;
        }
        
        .nav-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #D35400;
            border-radius: 4px;
            transition: width 0.3s;
        }
        
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .question-indicator {
            width: 40px;
            height: 40px;
            border: 2px solid #e0e0e0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }
        
        .question-indicator.answered {
            background: #D35400;
            color: white;
            border-color: #D35400;
        }
        
        .question-indicator.current {
            border-color: #ff7aa8;
            background: #ff7aa8;
            color: white;
        }
        
        .submit-section {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .submit-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-btn:hover {
            background: #218838;
        }
        
        .results-container {
            display: none;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .score-display {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .score-number {
            font-size: 3rem;
            font-weight: bold;
            color: #D35400;
        }
        
        .review-question {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
        }
        
        .correct-answer {
            color: #28a745;
            font-weight: bold;
        }
        
        .wrong-answer {
            color: #dc3545;
            font-weight: bold;
        }
        
        .explanation {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="assignment-container">
        <div class="assignment-header">
            <h1>Bài Tập Trắc Nghiệm: Python và Cài Đặt Môi Trường</h1>
            <p>15 câu hỏi - Thời gian: 30 phút - Điểm tối đa: 50/100</p>
        </div>

        <div class="timer-info">
            <div>
                <strong>Thời gian còn lại:</strong>
                <span class="timer" id="timer">Đang kiểm tra...</span>
            </div>
            <div>
                <strong>Câu hỏi:</strong>
                <span id="current-question">-</span> / <span id="total-questions">15</span>
            </div>
        </div>

        <!-- Quiz Container -->
        <div class="quiz-container" id="quizContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="question-grid" id="questionGrid">
                <!-- Question indicators will be generated here -->
            </div>

            <div id="questionsContainer">
                <!-- Questions will be loaded here -->
            </div>

            <div class="navigation">
                <button class="nav-btn" id="prevBtn" onclick="previousQuestion()" disabled>
                    <i class="fas fa-chevron-left"></i> Câu trước
                </button>
                
                <div>
                    <span id="questionStatus">Câu 1 / 15</span>
                </div>
                
                <button class="nav-btn" id="nextBtn" onclick="nextQuestion()">
                    Câu tiếp <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="submit-section">
                <button class="submit-btn" id="submitBtn" onclick="submitQuiz()">
                    <i class="fas fa-check"></i> Nộp Bài
                </button>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container" id="resultsContainer">
            <div class="score-display">
                <div class="score-number" id="finalScore">0</div>
                <div>điểm / 15 điểm (= <span id="scaledScore">0</span>/50 điểm)</div>
                <div>Thời gian hoàn thành: <span id="completionTime"></span></div>
            </div>
            
            <div id="reviewContainer">
                <!-- Review will be shown here -->
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, updateDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Quiz data will be loaded here
        let quizData = [];
        let currentQuestionIndex = 0;
        let userAnswers = {};
        let timeLeft = 30 * 60; // 30 minutes in seconds
        let timerInterval;
        let startTime;
        let quizCompleted = false; // Track quiz completion status
        let quizInstanceId = 'quiz-2c-' + Date.now(); // Unique instance ID

        // Initialize quiz when page loads
        window.addEventListener('DOMContentLoaded', function() {
            checkAssignmentCompletion();
        });

        // Check if user has already completed this assignment
        async function checkAssignmentCompletion() {
            // Wait for auth state to be determined
            return new Promise((resolve) => {
                const unsubscribe = onAuthStateChanged(auth, async (user) => {
                    unsubscribe(); // Stop listening after first check

                    if (!user) {
                        document.getElementById('timer').textContent = 'Cần đăng nhập';
                        document.getElementById('timer').style.color = '#dc3545';
                        return;
                    }

                    try {
                        console.log('Checking assignment 2 completion for user:', user.uid);
                        const assignmentDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-2-c"));

                        if (assignmentDoc.exists()) {
                            // User has already completed this assignment
                            console.log('Assignment 2 already completed, showing results');
                            const assignmentData = assignmentDoc.data();
                            console.log('Assignment 2 data:', assignmentData);
                            showCompletedAssignment(assignmentData);
                        } else {
                            // User hasn't completed this assignment yet
                            console.log('Assignment 2 not completed yet, starting new quiz');
                            // Reset timer display
                            document.getElementById('timer').textContent = '30:00';
                            document.getElementById('timer').style.color = '#D35400';
                            document.getElementById('current-question').textContent = '1';

                            loadQuizData();
                            startTimer();
                            startTime = new Date();
                            setupAntiCheat(); // Setup anti-cheat monitoring
                        }
                    } catch (error) {
                        console.error("Error checking assignment 2 completion:", error);
                        // If there's an error, allow user to take the quiz
                        // Reset timer display
                        document.getElementById('timer').textContent = '30:00';
                        document.getElementById('timer').style.color = '#D35400';
                        document.getElementById('current-question').textContent = '1';

                        loadQuizData();
                        startTimer();
                        startTime = new Date();
                        setupAntiCheat(); // Setup anti-cheat monitoring
                    }

                    resolve();
                });
            });
        }

        // Store references to event listeners for cleanup
        let visibilityChangeHandler = null;
        let blurHandler = null;

        // Setup anti-cheat mechanism
        function setupAntiCheat() {
            // Clean up any existing listeners first
            cleanupAntiCheat();

            // Track page visibility changes
            visibilityChangeHandler = function() {
                if (!quizCompleted && document.hidden) {
                    console.log(`[${quizInstanceId}] User left page during quiz - marking as cheating attempt`);
                    handleCheatingAttempt();
                }
            };
            document.addEventListener('visibilitychange', visibilityChangeHandler);

            // Track focus changes with delay to avoid false positives from alerts
            blurHandler = function() {
                if (!quizCompleted) {
                    // Add a small delay to check if quiz is still not completed
                    // This prevents false positives when user clicks on alert dialogs
                    setTimeout(() => {
                        if (!quizCompleted) {
                            console.log(`[${quizInstanceId}] Window lost focus during quiz`);
                            handleCheatingAttempt();
                        }
                    }, 100);
                }
            };
            window.addEventListener('blur', blurHandler);

            console.log(`[${quizInstanceId}] Anti-cheat setup completed`);
        }

        // Clean up anti-cheat event listeners
        function cleanupAntiCheat() {
            if (visibilityChangeHandler) {
                document.removeEventListener('visibilitychange', visibilityChangeHandler);
                visibilityChangeHandler = null;
            }
            if (blurHandler) {
                window.removeEventListener('blur', blurHandler);
                blurHandler = null;
            }
        }

        // Handle cheating attempt
        async function handleCheatingAttempt() {
            if (quizCompleted) return; // Already completed, ignore

            quizCompleted = true; // Mark as completed to prevent multiple saves
            cleanupAntiCheat(); // Remove anti-cheat event listeners
            if (timerInterval) {
                clearInterval(timerInterval); // Stop timer
            }

            // Save 0 score immediately
            await saveZeroScore();

            // Show cheating detection message
            showCheatingDetected();
        }

        // Save zero score when cheating detected
        async function saveZeroScore() {
            const user = auth.currentUser;
            if (!user) return;

            try {
                const assignmentData = {
                    assignmentId: 'assignment-2-c',
                    assignmentTitle: 'Bài Tập 2: Python và Cài Đặt Môi Trường',
                    score: 0,
                    totalQuestions: quizData ? quizData.length : 15,
                    completionTime: 0,
                    submittedAt: new Date().toISOString(),
                    userAnswers: userAnswers,
                    quizData: quizData,
                    cheatingDetected: true,
                    reason: 'Left page during quiz'
                };

                await setDoc(doc(db, "users", user.uid, "assignments", "assignment-2-c"), assignmentData);

                // Update user stats but don't add score
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    await updateDoc(doc(db, "users", user.uid), {
                        assignmentCount: (userData.assignmentCount || 0) + 1,
                        totalScore: userData.totalScore || 0, // No score added
                        lastAssignmentScore: 0,
                        lastAssignmentDate: new Date().toISOString(),
                        totalCompletionTime: userData.totalCompletionTime || 0
                    });
                }

                console.log('Zero score saved due to cheating detection');
            } catch (error) {
                console.error('Error saving zero score:', error);
            }
        }

        // Show cheating detected message
        function showCheatingDetected() {
            // Hide quiz container
            document.getElementById('quizContainer').style.display = 'none';

            // Show results container with cheating message
            document.getElementById('resultsContainer').style.display = 'block';

            // Clear results container and add notice
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = '';

            const cheatingNotice = document.createElement('div');
            cheatingNotice.style.cssText = `
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 20px;
                margin-bottom: 20px;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                border: 2px solid #bd2130;
            `;
            cheatingNotice.innerHTML = `
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i>
                <h3>Vi phạm quy định làm bài!</h3>
                <p>Hệ thống đã phát hiện bạn rời khỏi trang trong quá trình làm bài.</p>
                <p><strong>Kết quả: 0 điểm - Bài tập đã được đánh dấu hoàn thành</strong></p>
                <p>Bạn có thể xem đáp án để ôn tập, nhưng không thể làm lại.</p>
                <p style="font-size: 0.9em; margin-top: 15px;">Vui lòng liên hệ giáo viên nếu đây là lỗi hệ thống.</p>
            `;

            resultsContainer.appendChild(cheatingNotice);

            // Add view answers button if quiz data is available
            if (quizData && quizData.length > 0) {
                const viewAnswersButton = document.createElement('div');
                viewAnswersButton.style.cssText = 'text-align: center; margin: 20px 0;';
                viewAnswersButton.innerHTML = `
                    <button onclick="showDetailedResults()" style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 1rem;
                        margin: 10px;
                    ">
                        <i class="fas fa-eye"></i> Xem đáp án chi tiết
                    </button>
                `;
                resultsContainer.appendChild(viewAnswersButton);
            }
        }

        // Show detailed results for cheating case
        function showDetailedResults() {
            if (!quizData || quizData.length === 0) {
                alert('Không thể tải dữ liệu câu hỏi. Vui lòng thử lại sau.');
                return;
            }

            const reviewContainer = document.createElement('div');
            reviewContainer.innerHTML = '<h3>Xem lại đáp án:</h3>';

            let reviewHTML = '';
            quizData.forEach((question, index) => {
                const userAnswer = userAnswers[index];
                reviewHTML += `
                    <div class="review-question">
                        <div class="question-number">Câu ${index + 1}:</div>
                        <div class="question-text">${question.question}</div>
                        <div class="answer-review">
                            <div class="wrong-answer">
                                Bạn chọn: ${userAnswer !== undefined ? question.options[userAnswer] : 'Không trả lời'} ✗
                            </div>
                            <div class="correct-answer">Đáp án đúng: ${question.options[question.correct]} ✓</div>
                            ${question.explanation ? `<div class="explanation">${question.explanation}</div>` : ''}
                        </div>
                    </div>
                `;
            });

            reviewContainer.innerHTML = reviewHTML;

            // Hide the cheating notice and show review
            const cheatingNotice = document.querySelector('#resultsContainer > div');
            if (cheatingNotice) {
                cheatingNotice.style.display = 'none';
            }

            document.getElementById('resultsContainer').appendChild(reviewContainer);
        }

        // Load quiz data from external file
        async function loadQuizData() {
            try {
                // Import quiz data
                const module = await import('./quiz-data-2.js');
                quizData = module.default || quizQuestions2;
            } catch (error) {
                console.error('Error loading quiz data 2:', error);
                // Fallback to inline data if import fails
                quizData = quizQuestions2;
            }

            generateQuestionGrid();
            displayQuestion(0);
        }

        // Show completed assignment
        function showCompletedAssignment(assignmentData) {
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            const score = assignmentData.score || 0;
            const totalQuestions = assignmentData.totalQuestions || 15;
            const scaledScore = Math.round((score / totalQuestions) * 50);

            document.getElementById('finalScore').textContent = score;
            document.getElementById('scaledScore').textContent = scaledScore;
            document.getElementById('completionTime').textContent = assignmentData.completionTime || 'Không xác định';

            // Show review if available
            if (assignmentData.userAnswers && assignmentData.quizData) {
                showReview(assignmentData.userAnswers, assignmentData.quizData);
            }
        }

        // Timer functions
        function startTimer() {
            timerInterval = setInterval(() => {
                timeLeft--;
                updateTimerDisplay();

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    submitQuiz();
                }
            }, 1000);
        }

        function updateTimerDisplay() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            if (timeLeft <= 300) { // 5 minutes warning
                document.getElementById('timer').style.color = '#dc3545';
            }
        }

        // Generate question grid
        function generateQuestionGrid() {
            const grid = document.getElementById('questionGrid');
            grid.innerHTML = '';

            for (let i = 0; i < quizData.length; i++) {
                const indicator = document.createElement('div');
                indicator.className = 'question-indicator';
                indicator.textContent = i + 1;
                indicator.onclick = () => goToQuestion(i);
                grid.appendChild(indicator);
            }

            updateQuestionGrid();
        }

        // Update question grid
        function updateQuestionGrid() {
            const indicators = document.querySelectorAll('.question-indicator');
            indicators.forEach((indicator, index) => {
                indicator.classList.remove('current', 'answered');

                if (index === currentQuestionIndex) {
                    indicator.classList.add('current');
                } else if (userAnswers[index] !== undefined) {
                    indicator.classList.add('answered');
                }
            });
        }

        // Display question
        function displayQuestion(index) {
            const container = document.getElementById('questionsContainer');
            container.innerHTML = '';

            if (index >= 0 && index < quizData.length) {
                const question = quizData[index];
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question active';

                questionDiv.innerHTML = `
                    <div class="question-number">Câu ${index + 1}:</div>
                    <div class="question-text">${question.question}</div>
                    <ul class="options">
                        ${question.options.map((option, optIndex) => `
                            <li class="option ${userAnswers[index] === optIndex ? 'selected' : ''}"
                                onclick="selectAnswer(${index}, ${optIndex})">
                                <input type="radio" name="question${index}" value="${optIndex}"
                                       ${userAnswers[index] === optIndex ? 'checked' : ''}>
                                ${option}
                            </li>
                        `).join('')}
                    </ul>
                `;

                container.appendChild(questionDiv);
            }

            updateNavigation();
            updateProgress();
            updateQuestionGrid();
        }

        // Select answer
        function selectAnswer(questionIndex, optionIndex) {
            userAnswers[questionIndex] = optionIndex;

            // Update visual selection
            const options = document.querySelectorAll(`input[name="question${questionIndex}"]`);
            options.forEach((option, index) => {
                const li = option.parentElement;
                if (index === optionIndex) {
                    li.classList.add('selected');
                    option.checked = true;
                } else {
                    li.classList.remove('selected');
                    option.checked = false;
                }
            });

            updateQuestionGrid();
        }

        // Navigation functions
        function goToQuestion(index) {
            if (index >= 0 && index < quizData.length) {
                currentQuestionIndex = index;
                displayQuestion(index);
            }
        }

        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                displayQuestion(currentQuestionIndex);
            }
        }

        function nextQuestion() {
            if (currentQuestionIndex < quizData.length - 1) {
                currentQuestionIndex++;
                displayQuestion(currentQuestionIndex);
            }
        }

        function updateNavigation() {
            document.getElementById('prevBtn').disabled = currentQuestionIndex === 0;
            document.getElementById('nextBtn').disabled = currentQuestionIndex === quizData.length - 1;
            document.getElementById('questionStatus').textContent = `Câu ${currentQuestionIndex + 1} / ${quizData.length}`;
            document.getElementById('current-question').textContent = currentQuestionIndex + 1;
        }

        function updateProgress() {
            const progress = ((currentQuestionIndex + 1) / quizData.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // Submit quiz
        async function submitQuiz() {
            if (timerInterval) {
                clearInterval(timerInterval);
            }

            quizCompleted = true; // Mark quiz as completed to prevent anti-cheat
            cleanupAntiCheat(); // Remove anti-cheat event listeners

            const user = auth.currentUser;
            if (!user) {
                alert('Bạn cần đăng nhập để nộp bài!');
                return;
            }

            // Calculate score
            let score = 0;
            for (let i = 0; i < quizData.length; i++) {
                if (userAnswers[i] === quizData[i].correct) {
                    score++;
                }
            }

            const endTime = new Date();
            const timeTaken = Math.round((endTime - startTime) / 1000);
            const completionTime = formatTime(timeTaken);

            try {
                // Save to Firebase
                const assignmentData = {
                    score: score,
                    totalQuestions: quizData.length,
                    userAnswers: userAnswers,
                    quizData: quizData,
                    completionTime: completionTime,
                    submittedAt: new Date().toISOString(),
                    assignmentId: 'assignment-2-c'
                };

                await setDoc(doc(db, "users", user.uid, "assignments", "assignment-2-c"), assignmentData);

                // Update user's total score (50 points max for quiz part)
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    const currentTotalScore = userData.totalScore || 0;
                    const scaledScore = Math.round((score / quizData.length) * 50);
                    const newTotalScore = currentTotalScore + scaledScore;

                    await updateDoc(doc(db, "users", user.uid), {
                        assignmentCount: (userData.assignmentCount || 0) + 1,
                        totalScore: newTotalScore,
                        lastAssignmentScore: scaledScore,
                        lastAssignmentDate: new Date().toISOString(),
                        totalCompletionTime: (userData.totalCompletionTime || 0) + completionTime
                    });
                }

                console.log(`[${quizInstanceId}] Quiz 2-C results saved successfully`);

                // Show results immediately without alert
                showResults(score, completionTime);

            } catch (error) {
                console.error('Error saving quiz results:', error);
                alert('Có lỗi xảy ra khi lưu kết quả. Vui lòng thử lại!');
            }
        }

        // Show results
        function showResults(score, completionTime) {
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            const scaledScore = Math.round((score / quizData.length) * 50);
            document.getElementById('finalScore').textContent = score;
            document.getElementById('scaledScore').textContent = scaledScore;
            document.getElementById('completionTime').textContent = completionTime;

            // Add success notification to results
            const successNotice = document.createElement('div');
            successNotice.style.cssText = `
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                border: 2px solid #1e7e34;
            `;
            successNotice.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <strong>Nộp bài thành công!</strong> Điểm số đã được cập nhật vào bảng xếp hạng.
            `;
            document.getElementById('resultsContainer').insertBefore(successNotice, document.getElementById('resultsContainer').firstChild);

            showReview(userAnswers, quizData);
        }

        // Show review
        function showReview(answers, questions) {
            const container = document.getElementById('reviewContainer');
            container.innerHTML = '<h3>Xem lại đáp án:</h3>';

            questions.forEach((question, index) => {
                const userAnswer = answers[index];
                const isCorrect = userAnswer === question.correct;

                const reviewDiv = document.createElement('div');
                reviewDiv.className = 'review-question';
                reviewDiv.innerHTML = `
                    <div class="question-number">Câu ${index + 1}:</div>
                    <div class="question-text">${question.question}</div>
                    <div class="answer-review">
                        <div class="${isCorrect ? 'correct-answer' : 'wrong-answer'}">
                            Bạn chọn: ${userAnswer !== undefined ? question.options[userAnswer] : 'Không trả lời'}
                            ${isCorrect ? '✓' : '✗'}
                        </div>
                        ${!isCorrect ? `<div class="correct-answer">Đáp án đúng: ${question.options[question.correct]} ✓</div>` : ''}
                        ${question.explanation ? `<div class="explanation">${question.explanation}</div>` : ''}
                    </div>
                `;
                container.appendChild(reviewDiv);
            });
        }

        // Format time
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // Make functions global
        window.goToQuestion = goToQuestion;
        window.previousQuestion = previousQuestion;
        window.nextQuestion = nextQuestion;
        window.selectAnswer = selectAnswer;
        window.submitQuiz = submitQuiz;
    </script>

    <script src="quiz-data-2.js"></script>
</body>
</html>
