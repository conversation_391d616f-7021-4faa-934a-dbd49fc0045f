rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write their own profile data and assignments
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Allow users to read and write their own assignments, or admin to read all assignments
      match /assignments/{assignmentId} {
        allow read, write: if request.auth != null &&
          (request.auth.uid == userId || request.auth.token.email == '<EMAIL>');
      }
    }

    // Allow anyone (including guests) to read users data for public features like rankings and class listings
    match /users/{userId} {
      allow read: if true;
    }
    
    // Allow authenticated users to read and write assignment submissions
    match /assignments/{assignmentId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow authenticated users to read and write their own assignment submissions
    match /assignment_submissions/{submissionId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow authenticated users to read class data
    match /classes/{classId} {
      allow read: if request.auth != null;
    }
    
    // Allow authenticated users to read and write rankings data
    match /rankings/{rankingId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow anyone to read and write registration data (for public registration form)
    match /registrations/{registrationId} {
      allow read, write: if true;
    }
    
    // Admin-only access for user management (only for admin email)
    match /admin/{document=**} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
    
    // Allow anyone to read achievements data, authenticated users can write (for likes/views)
    match /achievements/{achievementId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Allow authenticated users to read research data
    match /research/{researchId} {
      allow read: if request.auth != null;
    }
  }
}
