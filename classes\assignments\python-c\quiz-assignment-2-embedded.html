<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>B<PERSON>i Tập Trắ<PERSON>: Python và Cài Đặt Môi Trường - Python C</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .assignment-container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #D35400, #E67E22);
            color: white;
            border-radius: 15px;
        }
        
        .assignment-header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .timer-info {
            background: white;
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .timer {
            font-size: 1.5rem;
            font-weight: bold;
            color: #D35400;
        }
        
        .quiz-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .question {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            display: none;
        }
        
        .question.active {
            display: block;
        }
        
        .question-number {
            color: #D35400;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .question-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .options {
            list-style: none;
            padding: 0;
        }
        
        .option {
            margin-bottom: 10px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            border-color: #D35400;
            background-color: #f8f9fa;
        }
        
        .option.selected {
            border-color: #D35400;
            background-color: #e3f2fd;
        }
        
        .option input[type="radio"] {
            margin-right: 10px;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
        }
        
        .nav-btn {
            background: #D35400;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }
        
        .nav-btn:hover {
            background: #3367D6;
        }
        
        .nav-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #D35400;
            border-radius: 4px;
            transition: width 0.3s;
        }
        
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .question-indicator {
            width: 40px;
            height: 40px;
            border: 2px solid #e0e0e0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }
        
        .question-indicator.answered {
            background: #D35400;
            color: white;
            border-color: #D35400;
        }
        
        .question-indicator.current {
            border-color: #ff7aa8;
            background: #ff7aa8;
            color: white;
        }
        
        .submit-section {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .submit-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-btn:hover {
            background: #218838;
        }
        
        .results-container {
            display: none;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .score-display {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .score-number {
            font-size: 3rem;
            font-weight: bold;
            color: #D35400;
        }
        
        .review-question {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
        }
        
        .correct-answer {
            color: #28a745;
            font-weight: bold;
        }
        
        .wrong-answer {
            color: #dc3545;
            font-weight: bold;
        }
        
        .explanation {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-style: italic;
        }

        .success-notice {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="assignment-container">
        <div class="assignment-header">
            <h1>Bài Tập Trắc Nghiệm: Python và Cài Đặt Môi Trường</h1>
            <p>15 câu hỏi - Thời gian: 30 phút - Điểm tối đa: 50/100</p>
        </div>

        <div class="timer-info">
            <div>
                <strong>Thời gian còn lại:</strong>
                <span class="timer" id="timer">Đang kiểm tra...</span>
            </div>
            <div>
                <strong>Câu hỏi:</strong>
                <span id="current-question">-</span> / <span id="total-questions">15</span>
            </div>
        </div>

        <!-- Quiz Container -->
        <div class="quiz-container" id="quizContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="question-grid" id="questionGrid">
                <!-- Question indicators will be generated here -->
            </div>

            <div id="questionsContainer">
                <!-- Questions will be loaded here -->
            </div>

            <div class="navigation">
                <button class="nav-btn" id="prevBtn" onclick="previousQuestion()" disabled>
                    <i class="fas fa-chevron-left"></i> Câu trước
                </button>
                
                <div>
                    <span id="questionStatus">Câu 1 / 15</span>
                </div>
                
                <button class="nav-btn" id="nextBtn" onclick="nextQuestion()">
                    Câu tiếp <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="submit-section">
                <button class="submit-btn" id="submitBtn" onclick="submitQuiz()">
                    <i class="fas fa-check"></i> Nộp Bài
                </button>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container" id="resultsContainer">
            <div class="score-display">
                <div class="score-number" id="finalScore">0</div>
                <div>điểm / 15 điểm (= <span id="scaledScore">0</span>/50 điểm)</div>
                <div>Thời gian hoàn thành: <span id="completionTime"></span></div>
            </div>
            
            <div id="reviewContainer">
                <!-- Review will be shown here -->
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, updateDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Quiz data will be loaded here
        let quizData = [];
        let currentQuestionIndex = 0;
        let userAnswers = {};
        let timeLeft = 30 * 60; // 30 minutes in seconds
        let timerInterval;
        let startTime;
        let quizCompleted = false;

        // Initialize quiz when page loads
        window.addEventListener('DOMContentLoaded', function() {
            checkAssignmentCompletion();
        });

        // Check if user has already completed this assignment
        async function checkAssignmentCompletion() {
            return new Promise((resolve) => {
                const unsubscribe = onAuthStateChanged(auth, async (user) => {
                    unsubscribe();

                    if (!user) {
                        document.getElementById('timer').textContent = 'Cần đăng nhập';
                        document.getElementById('timer').style.color = '#dc3545';
                        return;
                    }

                    try {
                        console.log('Checking quiz assignment 2-c completion for user:', user.uid);
                        const assignmentDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-2-c"));

                        if (assignmentDoc.exists()) {
                            console.log('Quiz assignment 2-c already completed, showing results');
                            const assignmentData = assignmentDoc.data();
                            showCompletedAssignment(assignmentData);
                        } else {
                            console.log('Quiz assignment 2-c not completed yet, starting new quiz');
                            document.getElementById('timer').textContent = '30:00';
                            document.getElementById('timer').style.color = '#D35400';
                            document.getElementById('current-question').textContent = '1';

                            loadQuizData();
                            startTimer();
                            startTime = new Date();
                        }
                    } catch (error) {
                        console.error("Error checking quiz assignment 2-c completion:", error);
                        document.getElementById('timer').textContent = '30:00';
                        document.getElementById('timer').style.color = '#D35400';
                        document.getElementById('current-question').textContent = '1';

                        loadQuizData();
                        startTimer();
                        startTime = new Date();
                    }

                    resolve();
                });
            });
        }

        // Load quiz data from external file
        async function loadQuizData() {
            try {
                const response = await fetch('quiz-data-2.js');
                const text = await response.text();

                // Extract the quiz data from the JavaScript file
                const match = text.match(/const quizData = (\[[\s\S]*?\]);/);
                if (match) {
                    quizData = JSON.parse(match[1]);
                    console.log('Quiz data loaded:', quizData.length, 'questions');

                    document.getElementById('total-questions').textContent = quizData.length;
                    generateQuestions();
                    generateQuestionGrid();
                    updateProgress();
                } else {
                    console.error('Could not parse quiz data');
                }
            } catch (error) {
                console.error('Error loading quiz data:', error);
            }
        }

        // Generate question HTML
        function generateQuestions() {
            const container = document.getElementById('questionsContainer');
            container.innerHTML = '';

            quizData.forEach((question, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.id = `question-${index}`;

                if (index === 0) {
                    questionDiv.classList.add('active');
                }

                questionDiv.innerHTML = `
                    <div class="question-number">Câu ${index + 1}:</div>
                    <div class="question-text">${question.question}</div>
                    <ul class="options">
                        ${question.options.map((option, optIndex) => `
                            <li class="option" onclick="selectOption(${index}, ${optIndex})">
                                <input type="radio" name="question-${index}" value="${optIndex}" id="q${index}o${optIndex}">
                                <label for="q${index}o${optIndex}">${option}</label>
                            </li>
                        `).join('')}
                    </ul>
                `;

                container.appendChild(questionDiv);
            });
        }

        // Generate question grid indicators
        function generateQuestionGrid() {
            const grid = document.getElementById('questionGrid');
            grid.innerHTML = '';

            quizData.forEach((_, index) => {
                const indicator = document.createElement('div');
                indicator.className = 'question-indicator';
                indicator.textContent = index + 1;
                indicator.onclick = () => goToQuestion(index);

                if (index === 0) {
                    indicator.classList.add('current');
                }

                grid.appendChild(indicator);
            });
        }

        // Start timer
        function startTimer() {
            timerInterval = setInterval(() => {
                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    submitQuiz();
                    return;
                }

                timeLeft--;
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                document.getElementById('timer').textContent =
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        // Select option
        window.selectOption = function(questionIndex, optionIndex) {
            userAnswers[questionIndex] = optionIndex;

            // Update visual selection
            const question = document.getElementById(`question-${questionIndex}`);
            const options = question.querySelectorAll('.option');
            options.forEach((option, index) => {
                option.classList.toggle('selected', index === optionIndex);
                const radio = option.querySelector('input[type="radio"]');
                radio.checked = index === optionIndex;
            });

            // Update question indicator
            const indicators = document.querySelectorAll('.question-indicator');
            indicators[questionIndex].classList.add('answered');

            updateProgress();
        };

        // Navigation functions
        window.previousQuestion = function() {
            if (currentQuestionIndex > 0) {
                goToQuestion(currentQuestionIndex - 1);
            }
        };

        window.nextQuestion = function() {
            if (currentQuestionIndex < quizData.length - 1) {
                goToQuestion(currentQuestionIndex + 1);
            }
        };

        window.goToQuestion = function(index) {
            // Hide current question
            document.getElementById(`question-${currentQuestionIndex}`).classList.remove('active');

            // Show new question
            currentQuestionIndex = index;
            document.getElementById(`question-${currentQuestionIndex}`).classList.add('active');

            // Update indicators
            const indicators = document.querySelectorAll('.question-indicator');
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('current', i === currentQuestionIndex);
            });

            // Update navigation buttons
            document.getElementById('prevBtn').disabled = currentQuestionIndex === 0;
            document.getElementById('nextBtn').disabled = currentQuestionIndex === quizData.length - 1;

            // Update status
            document.getElementById('questionStatus').textContent = `Câu ${currentQuestionIndex + 1} / ${quizData.length}`;
            document.getElementById('current-question').textContent = currentQuestionIndex + 1;
        };

        // Update progress bar
        function updateProgress() {
            const answeredCount = Object.keys(userAnswers).length;
            const progress = (answeredCount / quizData.length) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        // Submit quiz
        window.submitQuiz = function() {
            if (quizCompleted) return;

            const answeredCount = Object.keys(userAnswers).length;
            if (answeredCount < quizData.length) {
                if (!confirm(`Bạn chỉ trả lời ${answeredCount}/${quizData.length} câu hỏi. Bạn có chắc muốn nộp bài?`)) {
                    return;
                }
            }

            quizCompleted = true;
            if (timerInterval) {
                clearInterval(timerInterval);
            }

            calculateAndSaveResults();
        };

        // Calculate and save results
        async function calculateAndSaveResults() {
            const endTime = new Date();
            const completionTime = Math.round((endTime - startTime) / 1000); // in seconds

            let score = 0;
            const results = [];

            quizData.forEach((question, index) => {
                const userAnswer = userAnswers[index];
                const isCorrect = userAnswer === question.correct;

                if (isCorrect) {
                    score++;
                }

                results.push({
                    question: question.question,
                    options: question.options,
                    userAnswer: userAnswer,
                    correctAnswer: question.correct,
                    isCorrect: isCorrect,
                    explanation: question.explanation
                });
            });

            // Save to Firebase
            await saveQuizResults(score, completionTime, results);
        }

        // Save quiz results to Firebase
        async function saveQuizResults(score, completionTime, results) {
            const user = auth.currentUser;
            if (!user) return;

            try {
                const scaledScore = Math.round((score / quizData.length) * 50);

                const assignmentData = {
                    assignmentId: 'assignment-2-c',
                    assignmentTitle: 'Bài Tập Trắc Nghiệm: Python và Cài Đặt Môi Trường',
                    score: scaledScore,
                    totalScore: 50,
                    rawScore: score,
                    totalQuestions: quizData.length,
                    completionTime: completionTime,
                    completedAt: new Date().toISOString(),
                    results: results
                };

                await setDoc(doc(db, "users", user.uid, "assignments", "assignment-2-c"), assignmentData);
                console.log('Quiz 2-C results saved successfully');

                // Show completion message and redirect after 3 seconds
                showCompletionAndRedirect(scaledScore, score);

            } catch (error) {
                console.error('Error saving quiz results:', error);
            }
        }

        // Show completion message and redirect
        function showCompletionAndRedirect(scaledScore, rawScore) {
            // Hide quiz container
            document.getElementById('quizContainer').style.display = 'none';

            // Show completion message
            const completionDiv = document.createElement('div');
            completionDiv.innerHTML = `
                <div style="text-align: center; padding: 40px; background: white; border-radius: 15px; box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);">
                    <div style="font-size: 4rem; color: #28a745; margin-bottom: 20px;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h2 style="color: #D35400; margin-bottom: 20px;">Hoàn thành bài tập!</h2>
                    <p style="font-size: 1.2rem; margin-bottom: 10px;">
                        <strong>Điểm số:</strong> ${rawScore}/${quizData.length} câu đúng (${scaledScore}/50 điểm)
                    </p>
                    <p style="color: #28a745; font-weight: bold; margin-bottom: 30px;">
                        <i class="fas fa-trophy"></i> Điểm số đã được cập nhật vào bảng xếp hạng!
                    </p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <p style="margin: 0; color: #666;">
                            <i class="fas fa-clock"></i> Đang chuyển về trang lớp học sau <span id="countdown">3</span> giây...
                        </p>
                    </div>
                </div>
            `;

            document.querySelector('.assignment-container').appendChild(completionDiv);

            // Countdown and redirect
            let countdown = 3;
            const countdownElement = document.getElementById('countdown');

            const countdownInterval = setInterval(() => {
                countdown--;
                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }

                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    // Redirect to parent window (class page)
                    if (window.parent && window.parent !== window) {
                        window.parent.location.reload();
                    } else {
                        window.location.href = '../../python-c.html';
                    }
                }
            }, 1000);
        }

        // Show results
        function showResults(score, completionTime) {
            const scaledScore = Math.round((score / quizData.length) * 50);

            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            document.getElementById('finalScore').textContent = score;
            document.getElementById('scaledScore').textContent = scaledScore;

            const minutes = Math.floor(completionTime / 60);
            const seconds = completionTime % 60;
            document.getElementById('completionTime').textContent =
                `${minutes} phút ${seconds} giây`;

            // Show review
            showReview();
        }

        // Show completed assignment
        function showCompletedAssignment(assignmentData) {
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            document.getElementById('finalScore').textContent = assignmentData.rawScore || 0;
            document.getElementById('scaledScore').textContent = assignmentData.score || 0;

            const completionTime = assignmentData.completionTime || 0;
            const minutes = Math.floor(completionTime / 60);
            const seconds = completionTime % 60;
            document.getElementById('completionTime').textContent =
                `${minutes} phút ${seconds} giây`;

            // Show review if available
            if (assignmentData.results) {
                showReviewFromData(assignmentData.results);
            }
        }

        // Show review
        function showReview() {
            const reviewContainer = document.getElementById('reviewContainer');
            reviewContainer.innerHTML = '<h3>Xem lại đáp án:</h3>';

            quizData.forEach((question, index) => {
                const userAnswer = userAnswers[index];
                const isCorrect = userAnswer === question.correct;

                const reviewDiv = document.createElement('div');
                reviewDiv.className = 'review-question';

                reviewDiv.innerHTML = `
                    <div class="question-number">Câu ${index + 1}:</div>
                    <div class="question-text">${question.question}</div>
                    <div style="margin: 10px 0;">
                        <strong>Đáp án của bạn:</strong>
                        <span class="${isCorrect ? 'correct-answer' : 'wrong-answer'}">
                            ${userAnswer !== undefined ? question.options[userAnswer] : 'Không trả lời'}
                        </span>
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>Đáp án đúng:</strong>
                        <span class="correct-answer">${question.options[question.correct]}</span>
                    </div>
                    ${question.explanation ? `<div class="explanation">${question.explanation}</div>` : ''}
                `;

                reviewContainer.appendChild(reviewDiv);
            });
        }

        // Show review from saved data
        function showReviewFromData(results) {
            const reviewContainer = document.getElementById('reviewContainer');
            reviewContainer.innerHTML = '<h3>Xem lại đáp án:</h3>';

            results.forEach((result, index) => {
                const reviewDiv = document.createElement('div');
                reviewDiv.className = 'review-question';

                reviewDiv.innerHTML = `
                    <div class="question-number">Câu ${index + 1}:</div>
                    <div class="question-text">${result.question}</div>
                    <div style="margin: 10px 0;">
                        <strong>Đáp án của bạn:</strong>
                        <span class="${result.isCorrect ? 'correct-answer' : 'wrong-answer'}">
                            ${result.userAnswer !== undefined ? result.options[result.userAnswer] : 'Không trả lời'}
                        </span>
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>Đáp án đúng:</strong>
                        <span class="correct-answer">${result.options[result.correctAnswer]}</span>
                    </div>
                    ${result.explanation ? `<div class="explanation">${result.explanation}</div>` : ''}
                `;

                reviewContainer.appendChild(reviewDiv);
            });
        }
    </script>
</body>
</html>
