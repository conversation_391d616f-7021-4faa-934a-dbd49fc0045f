<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài 2: Python và Cài Đặt Môi Trường - Python A</title>
    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .assignment-container {
            max-width: 1200px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }
        
        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #4285F4, #ff7aa8);
            color: white;
            border-radius: 15px;
        }
        
        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .assignment-tabs {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .assignment-tab {
            background: white;
            border: 2px solid #4285F4;
            color: #4285F4;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            min-width: 200px;
            text-align: center;
        }
        
        .assignment-tab:hover {
            background: #4285F4;
            color: white;
            transform: translateY(-2px);
        }
        
        .assignment-tab.active {
            background: #4285F4;
            color: white;
            box-shadow: 0 4px 15px rgba(66, 133, 244, 0.3);
        }
        
        .assignment-content {
            display: none;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .assignment-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .score-display {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .score-item {
            display: inline-block;
            margin: 0 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .score-value {
            font-size: 2rem;
            font-weight: bold;
            color: #4285F4;
        }
        
        .score-label {
            color: #666;
            margin-top: 5px;
        }
        
        .back-link {
            display: inline-block;
            color: #4285F4;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .iframe-container {
            width: 100%;
            height: 80vh;
            border: none;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .completion-status {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .completion-status.incomplete {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .completion-status.not-started {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="assignment-container">
        <a href="../../python-a.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - A
        </a>

        <div class="assignment-header">
            <h1>Bài 2: Python và Cài Đặt Môi Trường</h1>
            <p>Bài học kết hợp: Trắc nghiệm (50 điểm) + Bài tập code (50 điểm) = 100 điểm</p>
        </div>

        <!-- Score Display -->
        <div class="score-display">
            <div class="score-item">
                <div class="score-value" id="quizScore">-</div>
                <div class="score-label">Điểm trắc nghiệm</div>
            </div>
            <div class="score-item">
                <div class="score-value" id="codingScore">-</div>
                <div class="score-label">Điểm code</div>
            </div>
            <div class="score-item">
                <div class="score-value" id="totalScore">-</div>
                <div class="score-label">Tổng điểm</div>
            </div>
        </div>

        <!-- Assignment Tabs -->
        <div class="assignment-tabs">
            <div class="assignment-tab active" onclick="showAssignment('quiz')">
                <i class="fas fa-question-circle"></i> Bài Tập Trắc Nghiệm
            </div>
            <div class="assignment-tab" onclick="showAssignment('coding')">
                <i class="fas fa-code"></i> Bài Tập Lập Trình
            </div>
        </div>

        <!-- Quiz Assignment -->
        <div class="assignment-content active" id="quizContent">
            <div class="completion-status" id="quizStatus">
                <i class="fas fa-info-circle"></i> Đang kiểm tra trạng thái bài tập trắc nghiệm...
            </div>
            <div class="iframe-container">
                <iframe src="quiz-assignment-2-embedded.html" id="quizFrame"></iframe>
            </div>
        </div>

        <!-- Coding Assignment -->
        <div class="assignment-content" id="codingContent">
            <div class="completion-status" id="codingStatus">
                <i class="fas fa-info-circle"></i> Đang kiểm tra trạng thái bài tập lập trình...
            </div>
            <div class="iframe-container">
                <iframe src="coding-assignment-2-embedded.html" id="codingFrame"></iframe>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Initialize when page loads
        window.addEventListener('DOMContentLoaded', function() {
            checkAssignmentStatuses();
        });

        // Check both assignment statuses
        async function checkAssignmentStatuses() {
            onAuthStateChanged(auth, async (user) => {
                if (!user) {
                    alert('Bạn cần đăng nhập để làm bài tập!');
                    window.location.href = '../../../auth/';
                    return;
                }

                try {
                    // Check quiz assignment
                    const quizDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-2"));
                    let quizScore = 0;
                    let quizCompleted = false;

                    if (quizDoc.exists()) {
                        const quizData = quizDoc.data();
                        quizScore = Math.round((quizData.score / quizData.totalQuestions) * 50); // Convert to 50 points
                        quizCompleted = true;
                        updateQuizStatus('completed', quizScore);
                    } else {
                        updateQuizStatus('not-started');
                    }

                    // Check coding assignment
                    const codingDoc = await getDoc(doc(db, "users", user.uid, "coding-assignments", "coding-assignment-2-a"));
                    let codingScore = 0;
                    let codingCompleted = false;

                    if (codingDoc.exists()) {
                        const codingData = codingDoc.data();
                        const completedProblems = codingData.completedProblems || [];
                        const skippedProblems = codingData.skippedProblems || [];
                        const totalAttempted = completedProblems.length + skippedProblems.length;

                        if (totalAttempted === 5) {
                            // Calculate coding score
                            let correctCount = 0;
                            const submissionResults = codingData.submissionResults || {};
                            for (let problemId of completedProblems) {
                                if (submissionResults[problemId] && submissionResults[problemId].isCorrect) {
                                    correctCount++;
                                }
                            }
                            codingScore = Math.round((correctCount / 5) * 50); // 50 points max
                            codingCompleted = true;
                            updateCodingStatus('completed', codingScore, correctCount);
                        } else {
                            updateCodingStatus('incomplete', 0, completedProblems.length);
                        }
                    } else {
                        updateCodingStatus('not-started');
                    }

                    // Update score display
                    document.getElementById('quizScore').textContent = quizCompleted ? `${quizScore}/50` : '-';
                    document.getElementById('codingScore').textContent = codingCompleted ? `${codingScore}/50` : '-';
                    document.getElementById('totalScore').textContent = 
                        (quizCompleted && codingCompleted) ? `${quizScore + codingScore}/100` : '-';

                } catch (error) {
                    console.error('Error checking assignment statuses:', error);
                }
            });
        }

        // Update quiz status
        function updateQuizStatus(status, score = 0) {
            const statusElement = document.getElementById('quizStatus');
            
            switch (status) {
                case 'completed':
                    statusElement.className = 'completion-status';
                    statusElement.innerHTML = `<i class="fas fa-check-circle"></i> Bài tập trắc nghiệm đã hoàn thành - Điểm: ${score}/50`;
                    break;
                case 'not-started':
                    statusElement.className = 'completion-status not-started';
                    statusElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> Chưa bắt đầu bài tập trắc nghiệm`;
                    break;
            }
        }

        // Update coding status
        function updateCodingStatus(status, score = 0, completed = 0) {
            const statusElement = document.getElementById('codingStatus');
            
            switch (status) {
                case 'completed':
                    statusElement.className = 'completion-status';
                    statusElement.innerHTML = `<i class="fas fa-check-circle"></i> Bài tập lập trình đã hoàn thành - Điểm: ${score}/50 (${completed}/5 bài đúng)`;
                    break;
                case 'incomplete':
                    statusElement.className = 'completion-status incomplete';
                    statusElement.innerHTML = `<i class="fas fa-clock"></i> Bài tập lập trình đang thực hiện - Đã nộp: ${completed}/5 bài`;
                    break;
                case 'not-started':
                    statusElement.className = 'completion-status not-started';
                    statusElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> Chưa bắt đầu bài tập lập trình`;
                    break;
            }
        }

        // Show assignment function
        function showAssignment(type) {
            // Hide all assignment contents
            document.querySelectorAll('.assignment-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.assignment-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected assignment
            document.getElementById(`${type}Content`).classList.add('active');
            
            // Add active class to selected tab
            const tabs = document.querySelectorAll('.assignment-tab');
            if (type === 'quiz') {
                tabs[0].classList.add('active');
            } else {
                tabs[1].classList.add('active');
            }
        }

        // Listen for messages from iframe
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'assignmentCompleted') {
                console.log('Assignment completed:', event.data);
                // Refresh assignment statuses
                setTimeout(() => {
                    checkAssignmentStatuses();
                }, 1000);
            }
        });

        // Make function global
        window.showAssignment = showAssignment;
    </script>

    <script src="../../../assets/js/script.js"></script>
</body>
</html>
